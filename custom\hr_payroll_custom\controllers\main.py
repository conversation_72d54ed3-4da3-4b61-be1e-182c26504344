# -*- coding: utf-8 -*-
from odoo import http, api, models, _, SUPERUSER_ID
from odoo.http import content_disposition, request
import io
from PyPDF2 import PdfFileReader, PdfFileWriter
from odoo import api, fields, models, _
from datetime import timedelta, date, datetime
from io import BytesIO
import xlwt
import xlsxwriter
import json



class ReportControllers(http.Controller):
    @http.route(['/report/summary-payslip/<model("summary.payslip"):wizard>',], type='http', auth="user", csrf=False)
    def print_xlsx(self,wizard=None,**args):
        check_batch = request.env['hr.payslip.run'].search([('date_start','=', wizard.date_from),
                                                            ('date_end','=', wizard.date_to)],
                                                            limit=1, order='id desc')
        response = request.make_response(
                    None,
                    headers=[
                        ('Content-Type', 'application/vnd.ms-excel'),
                        ('Content-Disposition', content_disposition(str(check_batch.name) + '.xlsx' if check_batch.name else 'Summary Payslip' + '.xlsx'))
                    ]
                )

        # Data
        #############################################################

        # Initialize
        #############################################################
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet('Payslip')

        # Format
        #############################################################
        format_title = workbook.add_format({
            'bold': True,
            'align': 'center',
            'font_size': 11,
            'font': 'Calibri',
            'border': True,
            'text_wrap': True,
            'font_color': 'white',
            'bg_color': '#0000FF',
            'valign': 'vcenter',
        })

        format_title_period = workbook.add_format({
            'bold': False,
            'align': 'left',
            'font_size': 11,
            'font': 'Times New Roman',
            'border': False,
            'text_wrap': True
        })

        format_header = workbook.add_format({
            'bold': True,
            'align': 'left',
            'font_size': 14,
            'font': 'Calibri',
            # 'border': True,
            # 'font_color': 'white',
            # 'bg_color': '#21159E'
        })

        format_header_2 = workbook.add_format({
            'bold': True,
            'align': 'left',
            'font_size': 12,
            'font': 'Calibri',
            # 'border': True,
            # 'font_color': 'white',
            # 'bg_color': '#21159E'
        })

        format_data = workbook.add_format({
            'bold': True,
            'align': 'center',
            'font_size': 11,
            'font': 'Calibri',
            'border': True,
            'num_format': '#,##0',
            'valign': 'vcenter'
        })

        format_data_1 = workbook.add_format({
            'bold': True,
            'align': 'left',
            'font_size': 11,
            'font': 'Calibri',
            'border': False,
            'num_format': '#,##0',
        })

        format_data_2 = workbook.add_format({
            'bold': False,
            'align': 'center',
            'font_size': 11,
            'font': 'Calibri',
            'border': True,
            'num_format': '#,##0',
        })

        format_data_3 = workbook.add_format({
            'bold': False,
            'align': 'left',
            'font_size': 11,
            'font': 'Calibri',
            'border': True,
            'num_format': '#,##0',
        })

        format_total = workbook.add_format({
            'bold': True,
            'align': 'center',
            'font_size': 11,
            'font': 'Calibri',
            'border': True,
            'num_format': '#,##0',
            'valign': 'vcenter'
        })

        # Write data
        ################################################################
        sheet.write(0, 0, 'Payslip Summary', format_header)
        sheet.write(1, 0, 'Date From: %s'%(wizard.date_from.strftime('%d/%m/%Y')), format_header_2)
        sheet.write(2, 0, 'Date To: %s'%(wizard.date_to.strftime('%d/%m/%Y')), format_header_2)

        departments = []
        employees = []

        for dept in wizard.department_ids.ids:
            departments.append(dept)
        for emp in wizard.employee_ids.ids:
            employees.append(emp)

        print(departments, employees)
        str_departments = str(tuple(departments))
        str_employees = str(tuple(employees))
        if len(departments) == 1:
            str_departments = str_departments.replace(',', '')
        if len(employees) == 1:
            str_employees = str_employees.replace(',','')

        row = 4
        sql_payslip = """
            select
                p.id as slip_id, e.name as employee, d.name as department,
                e.id as employee_id, p.date_from, p.date_to,
                coalesce(e.employee_number, '-') as employee_number,
                e.bank_account_number, e.bank_account_name, e.bank_account_holder,
                j.name as job_position, l.name as job_level
            from hr_payslip p
            left join hr_employee e on p.employee_id=e.id
            left join hr_department d on p.department_id=d.id
            left join hr_job j on e.job_id=j.id
            left join job_level l on e.job_level_inherit=l.id
            where p.department_id in %s and e.id in %s and p.date_from >= '%s' and p.date_to <= '%s' and p.type='%s'
            order by d.name, e.name
            """%(str_departments, str_employees, wizard.date_from, wizard.date_to, wizard.type)
        request.env.cr.execute(sql_payslip)
        print(sql_payslip)
        payslips = request.env.cr.dictfetchall()

        # Get list business trip type
        sql_trip_type = """
            select id, name from business_trip_type
            where active=true
            """
        request.env.cr.execute(sql_trip_type)
        trip_types = request.env.cr.dictfetchall()

        # Create Header Column
        list_col = []

        row += 1
        col = 0
        if not trip_types:
            sheet.write(row, col, "Employee Name", format_data)
            col += 1
            sheet.write(row, col, "Employee Number", format_data)
            col += 1
            sheet.write(row, col, "Department", format_data)
            col += 1
            sheet.write(row, col, "Job Postition", format_data)
            col += 1
            sheet.write(row, col, "Job Level", format_data)
        else:
            sheet.merge_range(row, col, row+1, col, "Employee Name", format_total)
            col += 1
            sheet.merge_range(row, col, row+1, col, "Employee Number", format_total)
            col += 1
            sheet.merge_range(row, col, row+1, col, "Department", format_total)
            col += 1
            sheet.merge_range(row, col, row+1, col, "Job Position", format_total)
            col += 1
            sheet.merge_range(row, col, row+1, col, "Job Level", format_total)

        data_rule = {}

        # Basic
        rules = request.env['hr.salary.rule'].search([('category_id.name','=', 'Basic')])
        for rule in rules:
            col += 1
            list_col.append(col)
            if not trip_types:
                sheet.write(row, col, rule.name, format_data)
            else:
                sheet.merge_range(row, col, row+1, col, rule.name, format_total)

            vals = {
                'name': rule.name,
                'col': col,
                'amount': 0
            }
            data_rule[rule.id] = vals

        # Allowance
        rules = request.env['hr.salary.rule'].search([('category_id.name','=', 'Allowance')])
        for rule in rules:
            if not trip_types:
                col += 1
                list_col.append(col)

                sheet.write(row, col, rule.name, format_data)
                vals = {
                    'name': rule.name,
                    'col': col,
                    'amount': 0
                }
                data_rule[rule.id] = vals
            else:
                if rule.name != 'Tunjangan Dinas Luar Kota':
                    col += 1
                    list_col.append(col)

                    sheet.merge_range(row, col, row+1, col, rule.name, format_data)
                    vals = {
                        'name': rule.name,
                        'col': col,
                        'amount': 0
                    }
                    data_rule[rule.id] = vals
                else:
                    sheet.merge_range(row, col+1, row, col+len(trip_types), rule.name, format_total)
                    vals = {
                        'name': rule.name,
                        'col': col,
                        'amount': 0,
                        'is_trip_type': True
                    }
                    data_rule[rule.id] = vals

                    trip_dict = {}

                    for trip_type in trip_types:
                        col += 1
                        list_col.append(col)

                        sheet.write(row+1, col, trip_type.get('name') or '', format_total)
                        vals_trip = {
                            'name': trip_type.get('name') or '',
                            'col': col,
                            'amount': 0
                        }
                        trip_dict[trip_type.get('id')] = vals_trip

        col += 1
        col_allowance = col
        if not trip_types:
            sheet.write(row, col, "Total Allowances", format_data)
        else:
            sheet.merge_range(row, col, row+1, col, "Total Allowances", format_total)

        # Deduction
        rules = request.env['hr.salary.rule'].search([('category_id.name','=', 'Deduction')])
        for rule in rules:
            col += 1
            list_col.append(col)
            if not trip_types:
                sheet.write(row, col, rule.name, format_data)
            else:
                sheet.merge_range(row, col, row+1, col, rule.name, format_total)

            vals = {
                'name': rule.name,
                'col': col,
                'amount': 0
            }
            data_rule[rule.id] = vals

        col += 1
        col_deduction = col
        if not trip_types:
            sheet.write(row, col, "Total Deductions", format_data)
        else:
            sheet.merge_range(row, col, row+1, col, "Total Deductions", format_total)

        col += 1
        last_col = col
        if not trip_types:
            sheet.write(row, col, "Take Home Pay", format_data)
            sheet.write(row, last_col+1, "Bank Account Number", format_data)
            sheet.write(row, last_col+2, "Bank Account Name", format_data)
            sheet.write(row, last_col+3, "Bank Account Holder", format_data)
        else:
            sheet.merge_range(row, col, row+1, col, "Take Home Pay", format_data)
            sheet.merge_range(row, last_col+1, row+1, last_col+1,"Bank Account Number", format_data)
            sheet.merge_range(row, last_col+2, row+1, last_col+2,"Bank Account Name", format_data)
            sheet.merge_range(row, last_col+3, row+1, last_col+3,"Bank Account Holder", format_data)

        if not trip_types:
            row += 0
        else:
            row += 1

        total_data = 0
        total_data_allowance = 0
        total_data_deduction = 0

        for payslip in payslips:
            copy_col_list = list_col.copy()
            row += 1
            col = 0
            sheet.write(row, col, payslip.get('employee'), format_data_3)
            col += 1
            sheet.write(row, col, payslip.get('employee_number'), format_data_3)
            col += 1
            sheet.write(row, col, payslip.get('department'), format_data_3)
            col += 1
            sheet.write(row, col, payslip.get('job_position'), format_data_3)
            col += 1
            sheet.write(row, col, payslip.get('job_level'), format_data_3)

            sql_line_payslip = """
                select c.name as category, l.name, l.code, l.amount, l.total_amount, l.salary_rule_id from hr_payslip_line l
                left join hr_salary_rule_category c on l.category_id=c.id
                where l.slip_id=%s
                """%(payslip.get('slip_id'))
            request.env.cr.execute(sql_line_payslip)
            line_payslip = request.env.cr.dictfetchall()
            total = 0
            total_allowance = 0
            total_deduction = 0

            for line in line_payslip:
                if data_rule[line.get('salary_rule_id')].get('is_trip_type'):
                    amount_perdin = 0
                    perdin = request.env['hr.payslip.worked_days'].search([('payslip_id','=', payslip.get('slip_id')),
                                                                           ('code','=', 'PERDIN100')], limit=1)
                    if perdin:
                        amount_perdin = perdin.amount

                    sql_trip = """
                        select
                            bt.id,
                            bt.name,
                            count(bt.id)*%s as total_amount
                        from hr_attendance a
                        left join hr_leave l on a.leave_id=l.id
                        left join hr_leave_type t on l.holiday_status_id=t.id
                        left join business_trip b on l.business_id=b.id
                        left join business_trip_type bt on b.type_id=bt.id
                        where
                            a.employee_id=%s
                            and a.leave_id is not null
                            and coalesce(t.is_business_trip, false)=true
                            and a.date >='%s'
                            and a.date <='%s'
                            and bt.id is not null
                        group by bt.id, bt.name
                        """%(amount_perdin, payslip.get('employee_id'), payslip.get('date_from'), payslip.get('date_to'))
                    request.env.cr.execute(sql_trip)
                    trips = request.env.cr.dictfetchall()
                    for trip in trips:
                        trip_id = trip.get('id')
                        # Check if trip_id exists and is in trip_dict to avoid KeyError
                        if trip_id and trip_id in trip_dict:
                            col = trip_dict[trip_id]['col']
                            copy_col_list.remove(col)
                            sheet.write(row, col, trip.get('total_amount'), format_data_2)

                    if line.get('category') in ['Basic', 'Allowance']:
                        total_allowance += line.get('total_amount')
                    elif line.get('category') == 'Deduction':
                        total_deduction += line.get('total_amount')

                    total += line.get('total_amount')
                else:
                    col = data_rule[line.get('salary_rule_id')]['col']
                    copy_col_list.remove(col)
                    sheet.write(row, col, line.get('total_amount'), format_data_2)

                    if line.get('category') in ['Basic', 'Allowance']:
                        total_allowance += line.get('total_amount')
                    elif line.get('category') == 'Deduction':
                        total_deduction += line.get('total_amount')

                    total += line.get('total_amount')

            sheet.write(row, last_col, total, format_data_2)
            total_data += total

            sheet.write(row, col_allowance, total_allowance, format_data_2)
            total_data_allowance += total_allowance

            sheet.write(row, col_deduction, total_deduction, format_data_2)
            total_data_deduction += total_deduction

            sheet.write(row, last_col+1, payslip.get('bank_account_number',''), format_data_3)
            sheet.write(row, last_col+2, payslip.get('bank_account_name',''), format_data_3)
            sheet.write(row, last_col+3, payslip.get('bank_account_holder',''), format_data_3)

            for col_empty in copy_col_list:
                sheet.write(row, col_empty, 0, format_data_2)

        row += 1

        sheet.merge_range(row, 0, row, 4, "Total", format_total)
        sheet.merge_range(row, 5, row, col_allowance-1, "", format_total)

        sheet.write(row, col_allowance, total_data_allowance, format_total)
        sheet.merge_range(row, col_allowance+1, row, col_deduction-1, "", format_total)
        sheet.write(row, col_deduction, total_data_deduction, format_total)

        sheet.write(row, last_col, total_data, format_total)
        sheet.merge_range(row, last_col+1, row, last_col+3, "", format_total)


        # Close and return
        #################################################################
        workbook.close()
        output.seek(0)
        response.stream.write(output.read())
        output.close()

        return response
